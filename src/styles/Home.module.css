.container {
  padding: 0;
}

.main {
  min-height: 100vh;
  padding: 4rem 0;
  width: 60vw;
  margin: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 900px;
}

.footer {
  display: flex;
  flex: 1;
  padding: 2rem 0;
  border-top: 1px solid #eaeaea;
  justify-content: center;
  align-items: center;
}

.footer a {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
}

.title a {
  color: #0d76fc;
  text-decoration: none;
}

.title a:hover,
.title a:focus,
.title a:active {
  text-decoration: underline;
}

.title {
  margin: 3rem 0;
  line-height: 1;
  font-size: 2.5rem;
}

.title,
.description {
  text-align: center;
}

.description {
  margin: 0 0 2rem;
  line-height: 1.5;
  font-size: 1.5rem;
}

.grid {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 800px;
}

.card {
  margin: 1rem;
  padding: 1.5rem;
  text-align: left;
  color: inherit;
  text-decoration: none;
  border: 1px solid #eaeaea;
  border-radius: 10px;
  transition: color 0.15s ease, border-color 0.15s ease;
  max-width: 350px;
}

.card:hover,
.card:focus,
.card:active {
  color: #0d76fc;
  border-color: #0d76fc;
}

.card h2 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.card p {
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.5;
}

@media (max-width: 600px) {
  .grid {
    width: 100%;
    flex-direction: column;
  }
}
