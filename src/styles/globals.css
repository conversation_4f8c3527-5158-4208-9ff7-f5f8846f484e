@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-100 antialiased;
    background-image: radial-gradient(
        circle at 100% 0%,
        rgba(14, 165, 233, 0.15) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 0% 100%,
        rgba(14, 165, 233, 0.15) 0%,
        transparent 50%
      );
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg 
           hover:from-primary-600 hover:to-primary-700 transition-all duration-300 ease-in-out 
           shadow-lg hover:shadow-primary-500/25 relative overflow-hidden;
  }

  .btn-primary::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0;
    transform: translateX(-100%);
    transition: transform 0.6s ease-in-out;
  }

  .btn-primary:hover::before {
    transform: translateX(100%);
  }

  .card {
    @apply bg-gray-800/50 backdrop-blur-xl rounded-2xl shadow-2xl 
           border border-gray-700/50 hover:border-primary-500/50
           transition-all duration-300 ease-in-out p-8;
  }

  .input-field {
    @apply w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg 
           focus:ring-2 focus:ring-primary-500 focus:border-primary-500 
           outline-none transition-all duration-200 text-gray-100
           placeholder-gray-500;
  }

  .tech-grid {
    @apply absolute inset-0 opacity-5 pointer-events-none;
    background-image: linear-gradient(to right, #0ea5e9 1px, transparent 1px),
      linear-gradient(to bottom, #0ea5e9 1px, transparent 1px);
    background-size: 24px 24px;
  }

  .glow {
    @apply relative;
  }

  .glow::after {
    content: "";
    @apply absolute -inset-0.5 bg-gradient-to-r from-primary-500 to-primary-600 
           rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-1000 
           group-hover:duration-200 animate-tilt;
    pointer-events: none;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-500/50 rounded-full hover:bg-primary-500 transition-colors duration-200;
}

/* 动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes tilt {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-tilt {
  animation: tilt 10s ease-in-out infinite;
}

/* 自定义 react-toastify 样式 */
.custom-toast {
  @apply bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-primary-500/30 shadow-xl rounded-xl text-gray-100 px-6 py-4 flex items-center backdrop-blur-lg;
  box-shadow: 0 4px 32px 0 rgba(14, 165, 233, 0.15);
}
.custom-toast-body {
  @apply text-gray-900 font-semibold drop-shadow;
}
.custom-toast-progress {
  @apply bg-primary-500;
  box-shadow: 0 0 8px 2px #0ea5e9;
}
